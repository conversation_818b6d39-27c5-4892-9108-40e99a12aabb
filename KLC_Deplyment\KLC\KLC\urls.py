from django.contrib import admin
from django.urls import path
from KLC_App import views
from django.conf.urls import handler404, handler500
from django.conf.urls.static import static
from django.conf import settings
from KLC_App.views import admin_reserve_hall, delete_reservation_admin, delete_transaction_admin

handler404 = 'KLC_App.views.custom_404'
handler500 = 'KLC_App.views.custom_500'

urlpatterns = [
    # Auth
    # path('login/', views.user_login, name='user_login'),
    # path('logout/', views.user_logout, name='user_logout'),

    # General pages
    path('', views.welcome , name='welcome'),
    path('index/', views.index , name='index'),
    path('check_person_id/', views.check_person_id , name='check_person_id'),
    path('services/', views.services , name='services'),
    path('services/reserve_hall/', views.reserve_hall , name='reserve_hall'),
    path('services/make_transaction/', views.make_transaction , name='make_transaction'),
    path('services/make_suggestions_complaints', views.make_suggestions_complaints , name='make_suggestions_complaints'),
    path('about_developers/', views.about_developers , name='about_developers'),
    # Protected admin pages
    path('admin/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/users/', views.admin_users, name='admin_users'),
    path('admin/reservations/', views.admin_reservations, name='admin_reservations'),
    path('admin/transactions/', views.admin_transactions, name='admin_transactions'),
    path('admin/suggestions/', views.admin_suggestions, name='admin_suggestions'),
    # path('add/', views.add_person, name='add_person'),
    # path('edit/<str:doc_id>/', views.edit_person, name='edit_person'),
    # path('delete/<str:doc_id>/', views.delete_person, name='delete_person'),

    path('admin/edit-user/<str:user_id>/', views.edit_user, name='edit_user'),
    path('admin/delete-user/<str:user_id>/', views.delete_user, name='delete_user'),
    # path('admin/settle-debt/<str:user_id>/', views.settle_debt, name='settle_debt'),
    path('admin/add-user/', views.add_user, name='add_user'),
    path('pay_debt/<user_id>/', views.settle_debt, name='settle_debt'),
    path('export-data/', views.export_data, name='export_data'),
    path('admin/reserve-hall/', admin_reserve_hall, name='admin_reserve_hall'),
    path('delete-reservation/<str:reservation_id>/', delete_reservation_admin, name='delete_reservation_admin'),
    path('delete-transaction/<str:transaction_id>/', delete_transaction_admin, name='delete_transaction_admin'),
    path('delete-completed-transaction/<str:transaction_id>/', views.delete_completed_transaction, name='delete_completed_transaction'),
    path('delete-suggestion/<str:suggestion_id>/', views.delete_suggestion_admin, name='delete_suggestion_admin'),
    path('confirm_reservation_admin/<str:reservation_id>/', views.confirm_reservation_admin, name='confirm_reservation_admin'),
    path('admin_login', views.admin_login, name='admin_login'),
    path('admin_logout/', views.admin_logout, name='admin_logout'),
    path('admin/admin_transaction/', views.admin_make_transaction , name='admin_make_transaction'),
    path('update-transaction-status/<str:transaction_id>/', views.update_transaction_status, name='update_transaction_status'),
    path('admin/transaction-statistics/', views.transaction_statistics, name='transaction_statistics'),

    # News Management
    path('admin/news/', views.admin_news, name='admin_news'),
    path('admin/add-news/', views.add_news, name='add_news'),
    path('admin/edit-news/<str:news_id>/', views.edit_news, name='edit_news'),
    path('admin/delete-news/<str:news_id>/', views.delete_news, name='delete_news'),

    # Public news pages
    path('news/', views.news_list, name='news_list'),
    path('news/<str:news_id>/', views.news_detail, name='news_detail'),

    # Admin Management
    path('admin/management/', views.admin_management, name='admin_management'),

    # New pages for reservations and transactions
    path('admin/add-reservation/', views.add_reservation, name='add_reservation'),
    path('admin/add-transaction/', views.add_transaction, name='add_transaction'),
]

# Add media URL patterns for all environments
# First try the Django built-in static function for all environments, including development and production
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Also add the custom media serving view as a fallback for all environments
# This ensures media files are served even if the static function doesn't work
urlpatterns += [
    path('media/<path:path>', views.serve_media_file, name='serve_media_file'),
]
