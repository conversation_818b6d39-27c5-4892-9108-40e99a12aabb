# Generated by Django 5.1.3 on 2025-02-02 08:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Person',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('national_id', models.<PERSON>r<PERSON>ield(max_length=100)),
                ('name', models.Char<PERSON>ield(max_length=1000)),
                ('phone', models.Char<PERSON>ield(max_length=15)),
                ('address', models.Char<PERSON>ield(max_length=100)),
                ('date_added', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
