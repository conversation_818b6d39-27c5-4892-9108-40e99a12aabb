/* Professional Modern News Grid Layout Styles */

/* News Grid Container */
.news-grid-container {
    margin-top: 2rem;
    position: relative;
}

/* News Detail Modal Styles */
.news-modal-content {
    position: relative;
}

/* Enhanced Professional News Gallery Styles */
.news-gallery-container {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background-color: #000;
}

.news-gallery {
    position: relative;
    width: 100%;
    height: 450px; /* Increased height for better viewing */
    overflow: hidden;
}

.news-gallery-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.6s ease, transform 0.5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #000;
    transform: scale(0.95);
}

.news-gallery-slide.active {
    opacity: 1;
    z-index: 2;
    transform: scale(1);
}

.news-gallery-slide img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease, opacity 0.3s ease;
    opacity: 1;
}

.news-gallery-slide:hover img {
    transform: scale(1.02);
}

/* Loading indicator for slides */
.slide-loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 1;
}

/* Loading state for images */
.news-gallery-slide img:not(.loaded) {
    opacity: 0;
}

.news-gallery-slide img.loaded {
    opacity: 1;
}

/* Video loading indicator */
.video-loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 1;
    transition: opacity 0.5s ease;
}

.video-container {
    position: relative;
}

.video-container.loaded .video-loading-indicator {
    opacity: 0;
    pointer-events: none;
}

.video-iframe {
    opacity: 0;
    transition: opacity 0.5s ease;
}

.video-container.loaded .video-iframe {
    opacity: 1;
}

.news-gallery-nav {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 12px;
    z-index: 10;
    padding: 10px 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
}

.gallery-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.gallery-dot.active {
    background-color: #dc3545; /* Changed to red as per user preference */
    transform: scale(1.2);
    border-color: rgba(255, 255, 255, 0.8);
}

.gallery-prev,
.gallery-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 45px;
    height: 45px;
    background-color: rgba(220, 53, 69, 0.8); /* Changed to red as per user preference */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.gallery-prev {
    left: 20px;
}

.gallery-next {
    right: 20px;
}

.gallery-prev:hover,
.gallery-next:hover {
    background-color: rgba(220, 53, 69, 1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.gallery-prev i,
.gallery-next i {
    font-size: 1.2rem;
    color: white;
}

/* Professional Thumbnails Gallery */
.gallery-thumbnails {
    display: flex;
    gap: 12px;
    margin-top: 15px;
    overflow-x: auto;
    padding: 5px 5px 15px;
    scrollbar-width: thin;
    scrollbar-color: #dc3545 #f1f1f1;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.1);
}

.gallery-thumbnails::-webkit-scrollbar {
    height: 8px;
}

.gallery-thumbnails::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.gallery-thumbnails::-webkit-scrollbar-thumb {
    background: #dc3545; /* Changed to red as per user preference */
    border-radius: 10px;
}

.gallery-thumbnail {
    width: 90px;
    height: 70px;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    flex-shrink: 0;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    position: relative;
}

.gallery-thumbnail::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    opacity: 1;
    transition: opacity 0.3s ease;
}

.gallery-thumbnail.active::after {
    opacity: 0;
}

.gallery-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-thumbnail:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.gallery-thumbnail:hover img {
    transform: scale(1.05);
}

.gallery-thumbnail.active {
    border-color: #dc3545; /* Changed to red as per user preference */
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

/* Enhanced Professional News Modal Styles */
.news-modal-meta {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-right: 4px solid #dc3545; /* Red accent border */
}

.news-modal-date,
.news-modal-category {
    display: flex;
    align-items: center;
    font-size: 1rem;
    color: #343a40;
    font-weight: 500;
}

.news-modal-date i,
.news-modal-category i {
    color: #dc3545; /* Changed to red as per user preference */
    margin-left: 10px;
    font-size: 1.2rem;
}

.news-modal-description {
    line-height: 1.9;
    color: #212529;
    font-size: 1.15rem;
    text-align: justify;
    margin-bottom: 30px;
    padding: 0 5px;
    font-weight: 400;
    border-right: 3px solid #f8f9fa;
    padding-right: 15px;
    transition: border-color 0.3s ease;
}

.news-modal-description:hover {
    border-right-color: #dc3545; /* Red accent on hover */
}

.news-modal-video {
    margin-top: 25px;
}

/* Professional Video Container */
.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    transform: scale(1);
    background-color: #000;
}

.video-container:hover {
    transform: scale(1.01);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

/* Professional Modal Styling */
#newsDetailModal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

#newsDetailModal .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.25rem 1.5rem;
}

#newsDetailModal .modal-title {
    font-weight: 700;
    color: #212529;
    font-size: 1.5rem;
    position: relative;
    padding-right: 15px;
}

#newsDetailModal .modal-title::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 70%;
    background-color: #dc3545; /* Red accent */
    border-radius: 3px;
}

#newsDetailModal .modal-body {
    padding: 1.5rem;
    background-color: #fff;
}

#newsDetailModal .modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.5rem;
}

/* Professional Button Styling */
#showVideoBtn {
    background-color: #dc3545; /* Changed to red as per user preference */
    border-color: #dc3545;
    color: white;
    font-weight: 600;
    padding: 0.5rem 1.25rem;
    border-radius: 30px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
}

#showVideoBtn:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(220, 53, 69, 0.4);
}

#showVideoBtn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
}

#newsDetailModal .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
    font-weight: 600;
    padding: 0.5rem 1.25rem;
    border-radius: 30px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(108, 117, 125, 0.3);
}

#newsDetailModal .btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(108, 117, 125, 0.4);
}

#newsDetailModal .btn-secondary:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(108, 117, 125, 0.3);
}

/* News Carousel */
#newsCarousel {
    position: relative;
    margin-bottom: 3rem;
}

/* Ensure carousel items have a minimum height to prevent layout shifts */
#newsCarousel .carousel-item {
    min-height: 600px; /* Minimum height for desktop */
}

/* Featured News Card (Large) */
.featured-news-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    height: 100%;
    transition: all 0.4s ease;
}

.featured-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.featured-img-container {
    position: relative;
    overflow: hidden;
    min-height: 600px; /* Increased from 400px to 500px */
    max-height: 800px; /* Increased from 600px to 700px */
    height: auto; /* Allow container to adjust based on content */
}

.featured-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.featured-news-card:hover .featured-img-container img {
    transform: scale(1.05);
}

.featured-news-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 3rem 2rem 2rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3), transparent);
    color: white;
    text-align: right;
}

.featured-news-category {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    padding: 0.4rem 1rem;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.featured-news-date {
    font-size: 0.95rem;
    opacity: 0.9;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.featured-news-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.featured-news-excerpt {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    opacity: 0.95;
    max-height: 8.5rem; /* Allow for more text (about 5-6 lines) */
    overflow: hidden;
}

.featured-news-buttons {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    position: relative;
    z-index: 10; /* Ensure buttons are above other elements */
}

.featured-news-buttons .btn {
    padding: 0.5rem 1.25rem;
    font-weight: 600;
    border-radius: 30px;
    font-size: 1rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    pointer-events: auto; /* Ensure buttons are always clickable */
    position: relative; /* Create a new stacking context */
}

.featured-news-buttons .btn:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    /* Removed transform to prevent button movement */
}

/* Medium News Card */
.medium-news-card {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    height: auto; /* Changed from 100% to auto to fit content */
    min-height: 120px; /* Minimum height for consistency */
    transition: all 0.4s ease;
    background-color: white;
    display: flex; /* Use flexbox for better content distribution */
    flex-direction: column;
}

.medium-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.medium-img-container {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.medium-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.medium-news-card:hover .medium-img-container img {
    transform: scale(1.05);
}

.news-category-tag {
    position: absolute;
    top: 12px;
    right: 12px;
    background-color: #e74c3c;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 30px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
    z-index: 2;
}

.medium-news-content {
    padding: 1.5rem;
    text-align: right;
    flex: 1; /* Allow content to take available space */
    display: flex;
    flex-direction: column;
}

.news-date {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.medium-news-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0.5rem 0 1rem;
    line-height: 1.4;
    min-height: 1.4rem; /* Minimum height for one line */
    max-height: 3.4rem; /* Maximum height for two lines */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    color: #2c3e50;
}

.medium-news-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: auto; /* Push buttons to the bottom */
}

.medium-news-buttons .btn {
    padding: 0.4rem 1rem;
    font-weight: 600;
    border-radius: 30px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.medium-news-buttons .btn:hover {
    transform: translateY(-3px);
}

/* Small News Card */
.small-news-card {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    height: auto; /* Changed from 100% to auto to fit content */
    min-height: 120px; /* Minimum height for consistency */
    transition: all 0.4s ease;
    background-color: white;
    display: flex; /* Use flexbox for better content distribution */
    flex-direction: column;
}

.small-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.small-img-container {
    position: relative;
    height: 100%;
    min-height: 140px; /* Reduced minimum height */
    max-height: 180px; /* Maximum height */
    overflow: hidden;
}

.small-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.small-news-card:hover .small-img-container img {
    transform: scale(1.05);
}

.small-news-category {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #e74c3c;
    color: white;
    padding: 0.25rem 0.7rem;
    border-radius: 30px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
    z-index: 2;
}

.small-news-content {
    padding: 1.25rem;
    text-align: right;
    flex: 1; /* Allow content to take available space */
    display: flex;
    flex-direction: column;
}

.small-news-date {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.small-news-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    line-height: 1.4;
    min-height: 1.4rem; /* Minimum height for one line */
    max-height: 3rem; /* Maximum height for two lines */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    color: #2c3e50;
}

.small-news-excerpt {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #6c757d;
    margin-bottom: 1rem;
    min-height: 1.5rem; /* Minimum height for one line */
    max-height: 4.5rem; /* Maximum height for three lines */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    flex: 1; /* Allow excerpt to take available space */
}

.small-news-buttons {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    margin-top: auto; /* Push buttons to the bottom */
}

.small-news-buttons .btn {
    padding: 0.35rem 0.9rem;
    font-weight: 600;
    border-radius: 30px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.small-news-buttons .btn:hover {
    transform: translateY(-3px);
}

/* Carousel Navigation Controls */
.news-navigation-controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    z-index: 20;
    pointer-events: auto;
    width: 100%;
}

.carousel-control-prev,
.carousel-control-next {
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: all 0.3s ease;
    pointer-events: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    border: 2px solid #f8f9fa;
    z-index: 20;
    position: relative;
    margin: 0 5px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: scale(1.1);
    border-color: #e74c3c;
}

.carousel-control-prev:active,
.carousel-control-next:active {
    transform: scale(0.95);
    background-color: #f8f9fa;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.carousel-control-icon {
    color: #2c3e50;
    font-size: 1.2rem;
}

.carousel-control-prev.disabled,
.carousel-control-next.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
    background-color: rgba(200, 200, 200, 0.7);
    box-shadow: none;
}

/* Professional News Pagination */
.news-pagination-container {
    margin: 2.5rem 0 1.5rem;
    position: relative;
    z-index: 10;
}

.news-pagination-info {
    margin-bottom: 1rem;
}

.page-info {
    display: inline-flex;
    align-items: center;
    background-color: #f8f9fa;
    color: #495057;
    padding: 0.6rem 1.5rem;
    border-radius: 30px;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    font-size: 1.1rem;
}

/* Autoplay control button */
.autoplay-control {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background-color: white;
    border-color: #ced4da;
}

.autoplay-control:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.autoplay-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25);
}

.autoplay-control.playing .fa-play {
    display: none;
}

.autoplay-control.paused .fa-pause {
    display: none;
}

.news-pagination {
    margin: 1.5rem 0;
}

.pagination {
    gap: 0.5rem;
}

.page-item .page-link {
    border-radius: 8px;
    padding: 0.8rem 1.2rem;
    color: #495057;
    font-weight: 600;
    border: 1px solid #dee2e6;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    background-color: white;
    font-size: 1rem;
    min-width: 45px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.page-item .page-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    color: #212529;
    border-color: #ced4da;
    z-index: 3;
}

.page-item.active .page-link {
    background-color: #e74c3c;
    border-color: #e74c3c;
    color: white;
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
    transform: translateY(-2px);
    z-index: 3;
}

.page-item.disabled .page-link {
    color: #adb5bd;
    pointer-events: none;
    background-color: #f8f9fa;
    border-color: #dee2e6;
    box-shadow: none;
}

#prevPageBtn, #nextPageBtn {
    padding: 0.8rem 1.5rem;
    font-weight: 600;
}

/* Dark theme support for pagination */
[data-theme="dark"] .page-info {
    background-color: #2c3e50;
    color: #ecf0f1;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .page-item .page-link {
    background-color: #34495e;
    border-color: #2c3e50;
    color: #ecf0f1;
}

[data-theme="dark"] .page-item .page-link:hover {
    background-color: #3d566e;
    border-color: #34495e;
}

[data-theme="dark"] .page-item.active .page-link {
    background-color: #e74c3c;
    border-color: #c0392b;
}

[data-theme="dark"] .page-item.disabled .page-link {
    background-color: #2c3e50;
    border-color: #2c3e50;
    color: #7f8c8d;
}

/* Dark Theme Support */
[data-theme="dark"] .medium-news-card,
[data-theme="dark"] .small-news-card {
    background-color: #2c3e50;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .medium-news-title,
[data-theme="dark"] .small-news-title {
    color: #ecf0f1;
}

[data-theme="dark"] .small-news-excerpt {
    color: #bdc3c7;
}

[data-theme="dark"] .small-news-date,
[data-theme="dark"] .news-date {
    color: #bdc3c7;
}

[data-theme="dark"] .carousel-control-prev,
[data-theme="dark"] .carousel-control-next {
    background-color: rgba(44, 62, 80, 0.9);
    border-color: #34495e;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .carousel-control-icon {
    color: #ecf0f1;
}

[data-theme="dark"] .carousel-control-prev:hover,
[data-theme="dark"] .carousel-control-next:hover {
    background-color: #34495e;
    border-color: #e74c3c;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .carousel-control-prev.disabled,
[data-theme="dark"] .carousel-control-next.disabled {
    opacity: 0.4;
    background-color: rgba(30, 40, 50, 0.7);
    box-shadow: none;
}

/* Row adjustments for news cards */
.news-grid-container .row {
    display: flex;
    flex-wrap: wrap;
}

.news-grid-container .row > [class*="col-"] {
    display: flex;
    flex-direction: column;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .featured-img-container {
        min-height: 380px;
        max-height: 500px;
    }

    .featured-news-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 992px) {
    .featured-img-container {
        min-height: 350px;
        max-height: 450px;
    }

    .featured-news-title {
        font-size: 1.6rem;
    }

    .featured-news-excerpt {
        font-size: 1rem;
    }

    .medium-img-container {
        height: 180px;
    }
}

@media (max-width: 768px) {
    /* Fix carousel item height for mobile to prevent layout shifts */
    #newsCarousel .carousel-item {
        min-height: 1000px; /* Ensure enough space for all content */
    }

    /* Fix for pages with only one row */
    #newsCarousel .carousel-item .row:last-child:only-child {
        min-height: 200px;
    }

    .featured-img-container {
        min-height: 300px;
        max-height: 400px;
    }

    .featured-news-title {
        font-size: 1.4rem;
    }

    .featured-news-excerpt {
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }

    .featured-news-buttons .btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }

    .medium-img-container {
        height: 160px;
    }

    .small-img-container {
        min-height: 160px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 40px;
        height: 40px;
    }

    /* News Gallery Responsive */
    .news-gallery {
        height: 350px;
    }

    .gallery-prev,
    .gallery-next {
        width: 35px;
        height: 35px;
    }

    .gallery-thumbnails {
        gap: 8px;
    }

    .gallery-thumbnail {
        width: 70px;
        height: 50px;
    }

    .news-modal-description {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    /* Fix carousel item height for small mobile to prevent layout shifts */
    #newsCarousel .carousel-item {
        min-height: 1200px; /* Ensure enough space for all content */
    }

    /* Fix for pages with only one row */
    #newsCarousel .carousel-item .row:last-child:only-child {
        min-height: 300px;
    }

    /* Ensure the second row has proper spacing */
    #newsCarousel .carousel-item .row + .row {
        margin-top: 1rem;
    }

    .featured-img-container {
        min-height: 250px;
        max-height: 350px;
    }

    .featured-news-overlay {
        padding: 2rem 1.5rem 1.5rem;
    }

    .featured-news-title {
        font-size: 1.3rem;
        margin-bottom: 0.75rem;
    }

    .featured-news-excerpt {
        font-size: 0.9rem;
        -webkit-line-clamp: 2;
        line-clamp: 2; /* Standard property for compatibility */
        height: 2.7rem;
        margin-bottom: 0.75rem;
    }

    .featured-news-buttons .btn {
        padding: 0.35rem 0.9rem;
        font-size: 0.85rem;
    }

    .medium-news-title,
    .small-news-title {
        font-size: 1rem;
    }

    .small-news-excerpt {
        font-size: 0.85rem;
        height: 3.8rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 35px;
        height: 35px;
    }

    .carousel-control-icon {
        font-size: 1rem;
    }

    .page-info {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .page-item .page-link {
        padding: 0.6rem 0.9rem;
        font-size: 0.9rem;
        min-width: 38px;
    }

    #prevPageBtn, #nextPageBtn {
        padding: 0.6rem 1.2rem;
    }

    /* Improve news card spacing */
    .news-grid-container .row {
        margin-left: -8px;
        margin-right: -8px;
    }

    .news-grid-container [class*="col-"] {
        padding-left: 8px;
        padding-right: 8px;
    }

    /* News Gallery Responsive */
    .news-gallery {
        height: 300px;
    }

    .gallery-prev,
    .gallery-next {
        width: 30px;
        height: 30px;
    }

    .gallery-prev i,
    .gallery-next i {
        font-size: 0.9rem;
    }

    .gallery-thumbnails {
        gap: 6px;
    }

    .gallery-thumbnail {
        width: 60px;
        height: 45px;
    }

    .gallery-dot {
        width: 10px;
        height: 10px;
    }

    .news-modal-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .news-modal-description {
        font-size: 0.95rem;
        line-height: 1.7;
    }

    .modal-dialog.modal-lg {
        max-width: 95%;
        margin: 0.5rem auto;
    }
}

/* Extra small devices (phones, 400px and down) */
@media (max-width: 400px) {
    .featured-img-container {
        min-height: 200px;
        max-height: 300px;
    }

    .featured-news-overlay {
        padding: 1.5rem 1.2rem 1.2rem;
    }

    .featured-news-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .featured-news-excerpt {
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
    }

    .featured-news-buttons .btn {
        padding: 0.3rem 0.7rem;
        font-size: 0.8rem;
    }

    .medium-news-title,
    .small-news-title {
        font-size: 0.95rem;
    }

    .small-news-excerpt {
        font-size: 0.8rem;
        height: 3.6rem;
    }

    .medium-img-container {
        height: 140px;
    }

    .small-img-container {
        min-height: 120px;
    }

    .medium-news-content,
    .small-news-content {
        padding: 1rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 30px;
        height: 30px;
    }

    .carousel-control-icon {
        font-size: 0.9rem;
    }

    .news-date,
    .small-news-date {
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }

    .page-info {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }

    .page-item .page-link {
        padding: 0.5rem 0.7rem;
        font-size: 0.85rem;
        min-width: 32px;
    }

    #prevPageBtn, #nextPageBtn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    /* Improve spacing */
    .news-grid-container .row {
        margin-left: -5px;
        margin-right: -5px;
    }

    .news-grid-container [class*="col-"] {
        padding-left: 5px;
        padding-right: 5px;
    }

    .news-grid-container .row.mb-4 {
        margin-bottom: 10px !important;
    }

    .news-grid-container .col-lg-6.mb-4 {
        margin-bottom: 10px !important;
    }

    /* News Gallery Responsive */
    .news-gallery {
        height: 250px;
    }

    .gallery-prev,
    .gallery-next {
        width: 28px;
        height: 28px;
    }

    .gallery-prev i,
    .gallery-next i {
        font-size: 0.8rem;
    }

    .gallery-thumbnails {
        gap: 5px;
    }

    .gallery-thumbnail {
        width: 50px;
        height: 40px;
    }

    .gallery-dot {
        width: 8px;
        height: 8px;
    }

    .news-modal-meta {
        padding: 10px;
        margin-bottom: 15px;
    }

    .news-modal-date,
    .news-modal-category {
        font-size: 0.85rem;
    }

    .news-modal-date i,
    .news-modal-category i {
        font-size: 1rem;
    }

    .news-modal-description {
        font-size: 0.9rem;
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .modal-dialog.modal-lg {
        max-width: 98%;
        margin: 0.3rem auto;
    }

    .modal-header {
        padding: 0.75rem;
    }

    .modal-body {
        padding: 0.75rem;
    }

    .modal-footer {
        padding: 0.75rem;
    }

    .modal-title {
        font-size: 1.1rem;
    }
}
