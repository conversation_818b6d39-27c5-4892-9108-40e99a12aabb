/* Professional Admin Dashboard Styles */

/* ===== VARIABLES ===== */
:root {
    /* Professional color palette */
    --primary-color: #3a7ca5;
    --primary-light: #d9e6f2;
    --primary-dark: #2c5d7c;

    --secondary-color: #81a4cd;
    --secondary-light: #e1ebf7;
    --secondary-dark: #5a789e;

    --accent-color: #d49a6a;
    --accent-light: #f7e9de;
    --accent-dark: #b37a4e;

    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;

    --text-dark: #333333;
    --text-medium: #555555;
    --text-light: #ffffff;

    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);

    /* Dark mode colors */
    --dark-bg-primary: #1a1a2e;
    --dark-bg-secondary: #16213e;
    --dark-bg-card: #0f3460;
    --dark-border-color: #2a2a4a;
}

/* ===== BACKGROUNDS ===== */
/* Main background with subtle pattern */
body {
    background-color: #f5f7fa;
    background-image: url('../images/patterns/subtle_dots.png');
    background-attachment: fixed;
}

[data-theme="dark"] body {
    background-color: var(--dark-bg-primary);
    background-image: url('../images/patterns/subtle_dots_dark.png');
}

/* Main content area */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

[data-theme="dark"] .main-content {
    background: rgba(26, 26, 46, 0.95);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Sidebar with gradient */
.sidebar {
    background: linear-gradient(180deg, #3a7ca5 0%, #2c5d7c 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .sidebar {
    background: linear-gradient(180deg, #16213e 0%, #0f3460 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    margin: 5px 10px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    border-right: 4px solid var(--accent-color);
}

/* Section headers */
.section-header {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

[data-theme="dark"] .section-header {
    border-bottom-color: var(--dark-border-color);
}

.section-header h2 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0;
}

[data-theme="dark"] .section-header h2 {
    color: var(--secondary-color);
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .card {
    background-color: var(--dark-bg-card);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.card-header {
    background: linear-gradient(90deg, var(--primary-light) 0%, rgba(255,255,255,0) 100%);
    border-bottom: 1px solid var(--border-color);
    padding: 1.2rem 1.5rem;
}

[data-theme="dark"] .card-header {
    background: linear-gradient(90deg, rgba(58, 124, 165, 0.2) 0%, rgba(15, 52, 96, 0.1) 100%);
    border-bottom: 1px solid var(--dark-border-color);
}

.card-header h5, .card-header h6 {
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    color: var(--primary-color);
}

[data-theme="dark"] .card-header h5,
[data-theme="dark"] .card-header h6 {
    color: var(--secondary-color);
}

.card-header h5 i, .card-header h6 i {
    margin-left: 0.5rem;
    color: var(--accent-color);
}

.card-body {
    padding: 1.5rem;
}

/* Dashboard summary cards */
.card.shadow-sm.h-100 {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card.shadow-sm.h-100:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

[data-theme="dark"] .card.shadow-sm.h-100 {
    background-color: var(--dark-bg-card) !important;
}

[data-theme="dark"] .card.shadow-sm.h-100:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

.card.shadow-sm.h-100 .rounded-circle {
    background-color: var(--primary-light) !important;
    color: var(--primary-color) !important;
    transition: all 0.3s ease;
}

[data-theme="dark"] .card.shadow-sm.h-100 .rounded-circle {
    background-color: rgba(58, 124, 165, 0.2) !important;
    color: var(--secondary-color) !important;
}

.card.shadow-sm.h-100:hover .rounded-circle {
    transform: scale(1.1);
}

/* ===== TABLES ===== */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table th {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    border: none;
}

[data-theme="dark"] .table th {
    background: linear-gradient(90deg, var(--dark-bg-card) 0%, var(--dark-bg-secondary) 100%);
    border-bottom: 1px solid var(--dark-border-color);
}

.table th:first-child {
    border-top-right-radius: 10px;
}

.table th:last-child {
    border-top-left-radius: 10px;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .table td {
    border-bottom: 1px solid var(--dark-border-color);
    color: var(--text-light);
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: var(--primary-light);
}

[data-theme="dark"] .table tbody tr:hover {
    background-color: rgba(58, 124, 165, 0.15);
}

/* Custom table styles */
.custom-table th {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    border-bottom: 2px solid var(--primary-dark) !important;
}

[data-theme="dark"] .custom-table th {
    background: linear-gradient(90deg, var(--dark-bg-card) 0%, var(--dark-bg-secondary) 100%) !important;
    border-bottom: 2px solid var(--dark-border-color) !important;
}

.custom-table tbody tr:nth-child(even) {
    background-color: rgba(58, 124, 165, 0.05);
}

.custom-table tbody tr:nth-child(odd) {
    background-color: #fff;
}

[data-theme="dark"] .custom-table tbody tr:nth-child(even) {
    background-color: rgba(58, 124, 165, 0.1);
}

[data-theme="dark"] .custom-table tbody tr:nth-child(odd) {
    background-color: var(--dark-bg-card);
}

.custom-table tbody tr:hover {
    background-color: var(--primary-light) !important;
}

[data-theme="dark"] .custom-table tbody tr:hover {
    background-color: rgba(58, 124, 165, 0.2) !important;
}

/* ===== DATATABLES ===== */
/* Search and length controls container */
.dataTables_wrapper .dt-controls {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--primary-light) 0%, rgba(255,255,255,0.8) 100%);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

[data-theme="dark"] .dataTables_wrapper .dt-controls {
    background: linear-gradient(135deg, rgba(58, 124, 165, 0.2) 0%, rgba(15, 52, 96, 0.1) 100%);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Search box styling */
.dataTables_wrapper .dataTables_filter {
    display: flex;
    align-items: center;
    position: relative;
    margin: 0;
}

.dataTables_wrapper .dataTables_filter label {
    display: flex;
    align-items: center;
    margin: 0;
    width: 100%;
    font-weight: bold;
    color: var(--primary-color);
}

[data-theme="dark"] .dataTables_wrapper .dataTables_filter label {
    color: var(--secondary-color);
}

.dataTables_wrapper .dataTables_filter input {
    margin: 0 0 0 0.5rem;
    padding: 0.6rem 1rem 0.6rem 2.5rem;
    border-radius: 50px;
    border: 1px solid var(--border-color);
    background-color: white;
    color: var(--text-dark);
    width: 250px;
    transition: all 0.3s ease;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_filter input {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-color);
    color: var(--text-light);
}

.dataTables_wrapper .dataTables_filter input:focus {
    box-shadow: 0 0 0 3px rgba(58, 124, 165, 0.2);
    border-color: var(--primary-color);
    outline: none;
    width: 300px;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_filter input:focus {
    box-shadow: 0 0 0 3px rgba(58, 124, 165, 0.3);
}

/* Add search icon */
.dataTables_wrapper .dataTables_filter::before {
    content: "\f002";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    z-index: 1;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_filter::before {
    color: var(--secondary-color);
}

/* Length (show entries) styling */
.dataTables_wrapper .dataTables_length {
    display: flex;
    align-items: center;
    margin: 0;
}

.dataTables_wrapper .dataTables_length label {
    display: flex;
    align-items: center;
    margin: 0;
    font-weight: bold;
    color: var(--primary-color);
}

[data-theme="dark"] .dataTables_wrapper .dataTables_length label {
    color: var(--secondary-color);
}

.dataTables_wrapper .dataTables_length select {
    margin: 0 0.5rem;
    padding: 0.5rem 2rem 0.5rem 1rem;
    border-radius: 50px;
    border: 1px solid var(--border-color);
    background-color: white;
    color: var(--text-dark);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233a7ca5'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.7rem center;
    background-size: 1.2rem;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_length select {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-color);
    color: var(--text-light);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2381a4cd'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
}

.dataTables_wrapper .dataTables_length select:focus {
    box-shadow: 0 0 0 3px rgba(58, 124, 165, 0.2);
    border-color: var(--primary-color);
    outline: none;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_length select:focus {
    box-shadow: 0 0 0 3px rgba(58, 124, 165, 0.3);
}

/* Pagination styling */
.dataTables_wrapper .dataTables_paginate {
    margin-top: 1.5rem;
    display: flex;
    justify-content: center;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.8rem;
    margin: 0 0.2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: white;
    color: var(--primary-color) !important;
    cursor: pointer;
    transition: all 0.2s ease;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-color);
    color: var(--secondary-color) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white !important;
    border-color: var(--primary-color);
    font-weight: bold;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(90deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
    border-color: var(--primary-dark);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current) {
    background-color: var(--primary-light);
    color: var(--primary-color) !important;
    border-color: var(--primary-color);
}

[data-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current) {
    background-color: rgba(58, 124, 165, 0.2);
    color: var(--secondary-color) !important;
    border-color: var(--secondary-color);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Info (Showing X to Y of Z entries) styling */
.dataTables_wrapper .dataTables_info {
    margin-top: 1.5rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    text-align: center;
    font-size: 0.9rem;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_info {
    background-color: rgba(58, 124, 165, 0.2);
    color: var(--secondary-color);
}

/* ===== BUTTONS ===== */
.btn {
    border-radius: 8px;
    padding: 0.6rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(90deg, var(--success-color) 0%, #66bb6a 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(90deg, var(--danger-color) 0%, #e57373 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(90deg, var(--warning-color) 0%, #ffb74d 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(90deg, var(--info-color) 0%, #64b5f6 100%);
    border: none;
}

/* ===== FORM CONTROLS ===== */
.form-control, .form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border-color);
    color: var(--text-light);
}

.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 3px rgba(58, 124, 165, 0.2);
    border-color: var(--primary-color);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    box-shadow: 0 0 0 3px rgba(58, 124, 165, 0.3);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-medium);
}

[data-theme="dark"] .form-label {
    color: var(--text-light);
}

/* ===== BADGES ===== */
.badge {
    padding: 0.5em 0.8em;
    font-weight: 600;
    border-radius: 6px;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
    color: #212529;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

.badge.bg-info {
    background-color: var(--info-color) !important;
}

/* ===== ACTIVITY DATE BADGE ===== */
.activity-date {
    color: var(--primary-color) !important;
    font-weight: bold;
    font-size: 0.85rem;
    background-color: var(--primary-light);
    padding: 3px 8px;
    border-radius: 12px;
}

[data-theme="dark"] .activity-date {
    background-color: rgba(58, 124, 165, 0.2) !important;
    color: var(--secondary-color) !important;
}

/* ===== RECEIPT NUMBER ===== */
td[style*="color: #2563eb"] {
    color: var(--primary-color) !important;
    font-weight: bold;
}

[data-theme="dark"] td[style*="color: #2563eb"] {
    color: var(--secondary-color) !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
    /* DataTables mobile adjustments */
    .dataTables_wrapper .dt-controls {
        flex-direction: column;
        gap: 1rem;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_length {
        width: 100%;
    }

    .dataTables_wrapper .dataTables_filter input {
        width: 100%;
        padding: 0.5rem 1rem 0.5rem 2.2rem;
    }

    .dataTables_wrapper .dataTables_filter input:focus {
        width: 100%;
    }

    .dataTables_wrapper .dataTables_length select {
        padding: 0.4rem 1.8rem 0.4rem 0.8rem;
    }

    .dataTables_wrapper .dataTables_paginate {
        overflow-x: auto;
        white-space: nowrap;
        width: 100%;
        padding-bottom: 0.5rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.4rem 0.6rem;
        font-size: 0.9rem;
    }

    .dataTables_wrapper .dataTables_info {
        margin-top: 1rem;
        font-size: 0.85rem;
    }

    /* Card adjustments */
    .card {
        margin-bottom: 1rem;
    }

    .card:hover {
        transform: none;
    }

    .card-header {
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Table adjustments */
    .table th {
        padding: 0.75rem;
        font-size: 0.8rem;
    }

    .table td {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    /* Button adjustments */
    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    /* Form adjustments */
    .form-control, .form-select {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    /* Modal adjustments */
    .modal-header {
        padding: 0.75rem 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        padding: 0.75rem 1rem;
    }

    /* Sidebar adjustments */
    .sidebar .nav-link {
        margin: 3px 8px;
        padding: 0.6rem 0.8rem;
    }

    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        transform: translateX(3px);
    }
}

/* Small mobile devices */
@media (max-width: 576px) {
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.35rem 0.5rem;
        font-size: 0.8rem;
    }

    .table th {
        padding: 0.6rem;
        font-size: 0.75rem;
    }

    .table td {
        padding: 0.6rem;
        font-size: 0.85rem;
    }

    .btn {
        padding: 0.35rem 0.7rem;
        font-size: 0.85rem;
    }

    .card-header h5, .card-header h6 {
        font-size: 1rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.main-content {
    animation: fadeIn 0.5s ease-out;
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* Stagger animation for cards in a row */
.row .card:nth-child(1) { animation-delay: 0.1s; }
.row .card:nth-child(2) { animation-delay: 0.2s; }
.row .card:nth-child(3) { animation-delay: 0.3s; }
.row .card:nth-child(4) { animation-delay: 0.4s; }
