/* News Image Carousel Styles */

/* Carousel Container */
.news-image-carousel {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: inherit;
    background-color: transparent;
}

/* Carousel Slides */
.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 1;
    background-color: transparent;
    /* Add transition for propagation animation */
    transition: opacity 0.8s ease-in-out;
}

/* Propagation animation keyframes */
@keyframes propagation-wave {
    0% {
        transform: scale(1);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.5;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.carousel-slide.active {
    opacity: 1;
    z-index: 2;
    animation: propagation-wave 0.8s ease-in-out;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Carousel Indicators */
.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 6px;
    z-index: 10;
    padding: 5px 0;
    padding-top: 5px;
    padding-bottom: 5px;
    margin: 0;
}

.carousel-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.3);
    opacity: 0.8;
    padding: 0;
    margin: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.carousel-indicator.active {
    background-color: #dd0606;
    transform: scale(1.2);
    opacity: 1;
    border: 1px solid rgba(255, 255, 255, 0.7);
}

/* Carousel Counter */
.carousel-counter {
    position: absolute;
    right: 10px;
    bottom: 10px;
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    padding: 3px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

/* Gallery Indicator */
.news-item-gallery-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    z-index: 5;
    display: flex;
    align-items: center;
    gap: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.news-item-gallery-indicator i {
    font-size: 0.8rem;
    color: #ffcc00; /* Yellow color for the icon */
}

/* Responsive Styles */
@media (max-width: 768px) {
    .carousel-indicators {
        bottom: 5px;
        gap: 5px;
        padding-top: 10px;
    }

    .carousel-indicator {
        width: 7px;
        height: 7px;
    }

    .carousel-counter {
        font-size: 0.7rem;
        padding: 1px 6px;
        right: 8px;
    }

    .news-item-gallery-indicator {
        font-size: 0.7rem;
        padding: 2px 6px;
    }
}

@media (max-width: 576px) {
    .carousel-indicators {
        bottom: 3px;
        gap: 4px;
        padding-top: 8px;
        padding-bottom: 3px;
    }

    .carousel-indicator {
        width: 5px;
        height: 5px;
    }

    .carousel-counter {
        font-size: 0.65rem;
        padding: 1px 5px;
        right: 5px;
    }

    .news-item-gallery-indicator {
        font-size: 0.65rem;
        padding: 2px 5px;
        top: 5px;
        right: 5px;
    }
}

/* News Detail Page Carousel */
.news-detail-carousel {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.news-detail-carousel .carousel-indicators {
    bottom: 15px;
}

.news-detail-carousel .carousel-indicator {
    width: 12px;
    height: 12px;
}

/* Carousel Navigation Buttons */
.carousel-control-prev,
.carousel-control-next {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 40px !important;
    height: 40px !important;
    background-color: rgba(220, 53, 69, 0.8) !important; /* Static red background */
    border-radius: 50% !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    z-index: 10 !important;
    opacity: 1 !important; /* Full opacity always */
    /* No transitions or animations */
    /* Prevent any movement on click */
    outline: none !important;
    box-shadow: none !important;
    -webkit-tap-highlight-color: transparent !important;
    user-select: none !important;
    margin: 0 !important;
    padding: 0 !important;
    transition: none !important;
    animation: none !important;
}

/* Prevent any movement on active/focus states */
.carousel-control-prev:active,
.carousel-control-next:active,
.carousel-control-prev:focus,
.carousel-control-next:focus,
.carousel-control-prev:hover,
.carousel-control-next:hover {
    outline: none !important;
    box-shadow: none !important;
    transform: translateY(-50%) !important; /* Keep the same transform */
    position: absolute !important; /* Ensure position doesn't change */
    top: 50% !important; /* Keep the same top position */
    background-color: rgba(220, 53, 69, 0.8) !important; /* Keep the same background */
    opacity: 1 !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* No hover effects for navigation buttons */

.carousel-control-prev {
    left: 10px;
}

.carousel-control-next {
    right: 10px;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 20px;
    height: 20px;
    background-size: 100% 100%;
    display: inline-block;
}

.carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

/* Static styling for different carousel types */
.news-image-carousel .carousel-control-prev,
.news-image-carousel .carousel-control-next {
    width: 35px !important;
    height: 35px !important;
    background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
}

.featured-news-carousel .carousel-control-prev,
.featured-news-carousel .carousel-control-next {
    width: 45px !important;
    height: 45px !important;
    background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
}

.news-detail-carousel .carousel-control-prev,
.news-detail-carousel .carousel-control-next {
    width: 50px !important;
    height: 50px !important;
    background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
}

/* Main News Item (Featured) Carousel red dotted indicators */
.main-news-image-container .news-image-carousel .carousel-indicators {
    bottom: 15px;
}

.main-news-image-container .news-image-carousel .carousel-indicator {
    width: 12px;
    height: 12px;
}

/* Featured News Carousel in index.html */
.featured-news-carousel {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5; /* Default z-index to ensure visibility */
}

.featured-news-carousel .carousel-slide {
    border-radius: 0;
}

.featured-news-carousel .carousel-indicators {
    bottom: 20px; /* Position at the bottom of the image */
    z-index: 15; /* Higher z-index to ensure visibility */
    display: flex;
    justify-content: center;
    gap: 10px; /* Increased gap for better touch targets */
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
}

.featured-news-carousel .carousel-indicator {
    width: 15px; /* Larger size for better visibility */
    height: 15px; /* Larger size for better visibility */
    background-color: #dc3545; /* Red color for indicators */
    opacity: 0.6;
    cursor: pointer; /* Ensure cursor shows it's clickable */
    border: 2px solid rgba(255, 255, 255, 0.7); /* White border for better visibility */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5); /* Shadow for depth */
    transition: all 0.3s ease; /* Smooth transition */
}

.featured-news-carousel .carousel-indicator:hover {
    opacity: 0.8;
    transform: scale(1.1);
}

.featured-news-carousel .carousel-indicator.active {
    opacity: 1;
    transform: scale(1.3);
    background-color: #ff0000; /* Brighter red for active state */
}

/* Hide counter in featured news carousel */
.featured-news-carousel .carousel-counter {
    display: none;
}

.featured-carousel-indicators {
    z-index: 20 !important; /* Ensure indicators are above everything */
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 10px;
    padding: 10px 0;
}

/* Ensure the featured news overlay is always on top of the carousel */
.featured-img-container {
    position: relative;
}

.featured-news-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 5; /* Higher than carousel but lower than indicators */
    background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3), transparent);
    padding: 3rem 2rem 2rem;
}

@media (max-width: 768px) {
    .news-detail-carousel .carousel-indicators {
        bottom: 10px;
    }

    .news-detail-carousel .carousel-indicator {
        width: 10px;
        height: 10px;
    }

    .main-news-image-container .news-image-carousel .carousel-indicators {
        bottom: 10px;
    }

    .main-news-image-container .news-image-carousel .carousel-indicator {
        width: 10px;
        height: 10px;
    }

    .featured-news-carousel .carousel-indicators,
    .featured-carousel-indicators {
        bottom: 15px !important; /* Position at the bottom of the image on mobile */
        gap: 8px !important; /* Slightly smaller gap on mobile */
    }

    .featured-news-carousel .carousel-indicator {
        width: 12px; /* Slightly smaller on mobile */
        height: 12px;
    }

    .featured-news-overlay {
        padding: 2rem 1.5rem 1.5rem;
    }

    .featured-news-excerpt {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .featured-img-container {
        min-height: 450px; /* Adjusted for mobile but still taller than before */
    }

    /* Static responsive navigation buttons */
    .carousel-control-prev,
    .carousel-control-next {
        width: 35px !important;
        height: 35px !important;
        background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 18px !important;
        height: 18px !important;
    }

    .news-image-carousel .carousel-control-prev,
    .news-image-carousel .carousel-control-next {
        width: 30px !important;
        height: 30px !important;
        background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
    }

    .featured-news-carousel .carousel-control-prev,
    .featured-news-carousel .carousel-control-next {
        width: 35px !important;
        height: 35px !important;
        background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
    }

    .news-detail-carousel .carousel-control-prev,
    .news-detail-carousel .carousel-control-next {
        width: 40px !important;
        height: 40px !important;
        background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
    }
}

@media (max-width: 576px) {
    .featured-news-carousel .carousel-indicators,
    .featured-carousel-indicators {
        bottom: 10px !important; /* Position at the bottom of the image on small mobile */
        gap: 6px !important; /* Even smaller gap on small mobile */
    }

    .featured-news-overlay {
        padding: 1.5rem 1rem 1rem;
    }

    .featured-news-excerpt {
        font-size: 0.8rem;
        line-height: 1.3;
        max-height: 3.9rem; /* Limit to 3 lines */
        overflow: hidden;
    }

    .featured-img-container {
        min-height: 400px; /* Adjusted for small mobile but still taller than before */
    }

    /* Static smaller navigation buttons for small mobile */
    .carousel-control-prev,
    .carousel-control-next {
        width: 30px !important;
        height: 30px !important;
        background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 15px !important;
        height: 15px !important;
    }

    .news-image-carousel .carousel-control-prev,
    .news-image-carousel .carousel-control-next {
        width: 25px !important;
        height: 25px !important;
        background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
    }

    .featured-news-carousel .carousel-control-prev,
    .featured-news-carousel .carousel-control-next {
        width: 30px !important;
        height: 30px !important;
        background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
    }

    .news-detail-carousel .carousel-control-prev,
    .news-detail-carousel .carousel-control-next {
        width: 35px !important;
        height: 35px !important;
        background-color: rgba(220, 53, 69, 0.8) !important; /* Consistent red background */
    }
}
