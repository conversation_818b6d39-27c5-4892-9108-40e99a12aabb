.body {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(to bottom, #ffffff, #f0f0f0);
    flex-direction: column; /* Ensure content stacks vertically */
}

.logo-container {
    text-align: center;
    animation: fadeIn 2s ease-in-out;
}

.logo {
    max-width: 40%;
    height: auto;
    animation: zoomIn 3s ease-in-out;
}

.text {
    margin-top: 20px;
    font-family: Arial, sans-serif;
    color: #333333;
    font-size: 1.5rem;
    animation: slideUp 3s ease-in-out;
    text-align: center; /* Center align the text */
}

.text p {
    margin: 5px 0;
}

.button-container {
    text-align: center;
    visibility: hidden;
    margin-top: 20px;
    animation: slideUp 4s ease-in-out;
}

.button {
    font-size: 18px;
    padding: 10px 20px;
    background-color: #007BFF;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.button:hover {
    background-color: #0056b3;
    transform: scale(1.05); /* Slight zoom effect on hover */
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes zoomIn {
    from {
        transform: scale(0.8);
    }
    to {
        transform: scale(1);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
